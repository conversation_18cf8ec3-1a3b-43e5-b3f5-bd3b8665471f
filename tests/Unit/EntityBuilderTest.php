<?php

namespace Tests\Unit;

use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use ReflectionClass;

class TestEntity extends BaseEntity
{
    public static string $table = 'test_table';
    public static array $fields = ['id', 'name', 'description'];

    public static function getTable(): string
    {
        return self::$table;
    }

    public static function getFields(): array
    {
        return self::$fields;
    }
}

class EntityBuilderTest extends TestCase
{
    public function test_map_row_returns_object()
    {
        // Создаем мок для BaseEntity
        $entity = new TestEntity();
        
        // Создаем EntityBuilder
        $builder = new EntityBuilder($entity);
        
        // Получаем доступ к приватному методу mapRow через рефлексию
        $reflection = new ReflectionClass($builder);
        $method = $reflection->getMethod('mapRow');
        $method->setAccessible(true);
        
        // Создаем тестовые данные
        $row = new \stdClass();
        $row->id = '123';
        $row->name = 'Test Name';
        $row->description = 'Test Description';
        
        // Вызываем метод
        $result = $method->invoke($builder, $row);
        
        // Проверяем, что результат - это объект
        $this->assertIsObject($result);
        $this->assertEquals('123', $result->id);
        $this->assertEquals('Test Name', $result->name);
        $this->assertEquals('Test Description', $result->description);
    }
}
