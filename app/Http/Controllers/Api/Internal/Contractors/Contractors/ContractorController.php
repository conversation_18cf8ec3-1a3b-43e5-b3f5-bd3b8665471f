<?php

namespace App\Http\Controllers\Api\Internal\Contractors\Contractors;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Contractors\ContractorPolicyContract;
use App\Contracts\Repositories\ContractorsRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Contractors\ContractorsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Contractors\ContractorBulkRequest;
use App\Http\Requests\Api\Internal\Contractors\ContractorCreateRequest;
use App\Http\Requests\Api\Internal\Contractors\ContractorsIndexRequest;
use App\Http\Requests\Api\Internal\Contractors\ContractorUpdateRequest;
use App\Http\Resources\ContractorCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractorController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ContractorsServiceContract $service,
        private readonly ContractorPolicyContract $policy
    ) {
    }

    /**
     * List available contractors.
     *
     * @response ContractorCollection<ContractorResource>
     */
    public function index(ContractorsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            return $this->successResponse(new ContractorCollection($data));
        });
    }

    public function store(ContractorCreateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(ContractorUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkCopy(ContractorBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkDelete(ContractorBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        ContractorBulkRequest $request,
        ArchiveServiceContract $archiveService,
        ContractorsRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        ContractorBulkRequest $request,
        ArchiveServiceContract $archiveService,
        ContractorsRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
