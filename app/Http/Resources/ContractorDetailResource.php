<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorDetailResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $result = array_filter([
            'taxation_type' => $this->taxation_type,
            'type' => $this->type,
            'inn' => $this->inn,
            'kpp' => $this->kpp,
            'ogrn' => $this->ogrn,
            'okpo' => $this->okpo,
            'full_name' => $this->full_name,
            'firstname' => $this->firstname,
            'patronymic' => $this->patronymic,
            'lastname' => $this->lastname,
            'ogrnip' => $this->ogrnip,
            'certificate_number' => $this->certificate_number,
            'certificate_date' => $this->certificate_date,
        ], fn($value) => $value !== null);

        if (isset($this->address) && !empty($this->address)) {
            $result['address'] = (object)$this->address;
        }

        return $result;
    }
}
