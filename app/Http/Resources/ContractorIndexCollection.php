<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContractorIndexCollection extends ResourceCollection
{
    public $collects = ContractorIndexResource::class;

    public function toArray(Request $request): array
    {
        // EntityBuilder возвращает Collection с ключами 'data' и 'meta'
        if ($this->resource instanceof \Illuminate\Support\Collection && $this->resource->has('data') && $this->resource->has('meta')) {
            $data = $this->resource->get('data');
            $meta = $this->resource->get('meta');
            
            return [
                /** @var array<ContractorIndexResource> */
                'data' => ContractorIndexResource::collection($data),
                /** @var array */
                'meta' => $meta,
            ];
        }

        // Fallback для стандартной коллекции
        return [
            /** @var array<ContractorIndexResource> */
            'data' => ContractorIndexResource::collection($this->collection),
            /** @var array */
            'meta' => [],
        ];
    }
}
