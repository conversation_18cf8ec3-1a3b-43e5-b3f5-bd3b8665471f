<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContractorCollection extends ResourceCollection
{
    public $collects = ContractorResource::class;
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var array<ContractorResource> */
            'data' => $this->collection['data'],
            /** @var array */
            'meta' => $this->collection['meta'],
        ];
    }
}
