<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $data = is_array($this->resource) ? $this->resource : (array) $this->resource;
        
        $result = array_filter([
            'id' => $data['id'] ?? null,
            'title' => $data['title'] ?? null,
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
            'deleted_at' => $data['deleted_at'] ?? null,
            'archived_at' => $data['archived_at'] ?? null,
            'cabinet_id' => $data['cabinet_id'] ?? null,
            'employee_id' => $data['employee_id'] ?? null,
            'department_id' => $data['department_id'] ?? null,
            'status_id' => $data['status_id'] ?? null,
            'phone' => $data['phone'] ?? null,
            'fax' => $data['fax'] ?? null,
            'email' => $data['email'] ?? null,
            'description' => $data['description'] ?? null,
            'code' => $data['code'] ?? null,
            'external_code' => $data['external_code'] ?? null,
            'discounts_and_prices' => $data['discounts_and_prices'] ?? null,
            'discount_card_number' => $data['discount_card_number'] ?? null,
        ], fn($value) => $value !== null);

        /** @var bool */
        $result['shared_access'] = $data['shared_access'] ?? false;
        /** @var bool */
        $result['is_buyer'] = $data['is_buyer'] ?? false;
        /** @var bool */
        $result['is_supplier'] = $data['is_supplier'] ?? false;
        /** @var bool */
        $result['is_default'] = $data['is_default'] ?? false;

        // Все связи для show (полная информация)
        if (isset($data['accounts']) && !empty($data['accounts'])) {
            /** @var array */
            $result['accounts'] = $this->processAccounts($data['accounts']);
        }

        if (isset($data['contacts']) && !empty($data['contacts'])) {
            /** @var array */
            $result['contacts'] = $this->processContacts($data['contacts']);
        }

        if (isset($data['contractor_groups']) && !empty($data['contractor_groups'])) {
            /** @var array */
            $result['contractor_groups'] = $this->processContractorGroups($data['contractor_groups']);
        }

        if (isset($data['detail']) && !empty($data['detail'])) {
            /** @var object */
            $result['detail'] = $this->processDetail($data['detail']);
        }

        if (isset($data['address']) && !empty($data['address'])) {
            /** @var object */
            $result['address'] = $this->processAddress($data['address']);
        }

        if (isset($data['files']) && !empty($data['files'])) {
            /** @var array */
            $result['files'] = $this->processFiles($data['files']);
        }

        return $result;
    }

    private function processAccounts($accounts): array
    {
        if (is_string($accounts)) {
            $accounts = json_decode($accounts, true);
        }

        if (!is_array($accounts) || empty($accounts)) {
            return [];
        }

        return array_map(function ($account) {
            return array_filter([
                'id' => $account['id'] ?? null,
                'is_main' => $account['is_main'] ?? false,
                'bik' => $account['bik'] ?? null,
                'correspondent_account' => $account['correspondent_account'] ?? null,
                'payment_account' => $account['payment_account'] ?? null,
                'bank' => $account['bank'] ?? null,
                'address' => $account['address'] ?? null,
                'created_at' => $account['created_at'] ?? null,
                'updated_at' => $account['updated_at'] ?? null,
            ], fn($value) => $value !== null);
        }, $accounts);
    }

    private function processContacts($contacts): array
    {
        if (is_string($contacts)) {
            $contacts = json_decode($contacts, true);
        }

        if (!is_array($contacts) || empty($contacts)) {
            return [];
        }

        return array_map(function ($contact) {
            return array_filter([
                'id' => $contact['id'] ?? null,
                'contractor_id' => $contact['contractor_id'] ?? null,
                'full_name' => $contact['full_name'] ?? null,
                'position' => $contact['position'] ?? null,
                'phone' => $contact['phone'] ?? null,
                'email' => $contact['email'] ?? null,
                'comment' => $contact['comment'] ?? null,
                'created_at' => $contact['created_at'] ?? null,
                'updated_at' => $contact['updated_at'] ?? null,
            ], fn($value) => $value !== null);
        }, $contacts);
    }

    private function processContractorGroups($groups): array
    {
        if (is_string($groups)) {
            $groups = json_decode($groups, true);
        }

        if (!is_array($groups) || empty($groups)) {
            return [];
        }

        return array_map(function ($group) {
            return array_filter([
                'group_id' => $group['group_id'] ?? null,
                'contractor_group_name' => $group['contractor_group_name'] ?? null,
            ], fn($value) => $value !== null);
        }, $groups);
    }

    private function processDetail($detail): array
    {
        if (is_string($detail)) {
            $detail = json_decode($detail, true);
        }

        if (empty($detail)) {
            return [];
        }

        $result = array_filter([
            'taxation_type' => $detail['taxation_type'] ?? null,
            'type' => $detail['type'] ?? null,
            'inn' => $detail['inn'] ?? null,
            'kpp' => $detail['kpp'] ?? null,
            'ogrn' => $detail['ogrn'] ?? null,
            'okpo' => $detail['okpo'] ?? null,
            'full_name' => $detail['full_name'] ?? null,
            'firstname' => $detail['firstname'] ?? null,
            'patronymic' => $detail['patronymic'] ?? null,
            'lastname' => $detail['lastname'] ?? null,
            'ogrnip' => $detail['ogrnip'] ?? null,
            'certificate_number' => $detail['certificate_number'] ?? null,
            'certificate_date' => $detail['certificate_date'] ?? null,
        ], fn($value) => $value !== null);

        if (isset($detail['address']) && !empty($detail['address'])) {
            $result['address'] = $this->processAddress($detail['address']);
        }

        return $result;
    }

    private function processAddress($address): array
    {
        if (is_string($address)) {
            $address = json_decode($address, true);
        }

        if (empty($address)) {
            return [];
        }

        return array_filter([
            'postcode' => $address['postcode'] ?? null,
            'country' => $address['country'] ?? null,
            'region' => $address['region'] ?? null,
            'city' => $address['city'] ?? null,
            'street' => $address['street'] ?? null,
            'house' => $address['house'] ?? null,
            'office' => $address['office'] ?? null,
            'other' => $address['other'] ?? null,
            'comment' => $address['comment'] ?? null,
        ], fn($value) => $value !== null);
    }

    private function processFiles($files): array
    {
        if (is_string($files)) {
            $files = json_decode($files, true);
        }

        if (!is_array($files) || empty($files)) {
            return [];
        }

        return array_map(function ($file) {
            return array_filter([
                'id' => $file['id'] ?? null,
                'name' => $file['name'] ?? null,
                'size' => $file['size'] ?? null,
                'path' => $file['path'] ?? null,
                'is_private' => $file['is_private'] ?? false,
            ], fn($value) => $value !== null);
        }, $files);
    }
}
