<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $data = is_array($this->resource) ? $this->resource : (array) $this->resource;
        
        $result = array_filter([
            'id' => $data['id'] ?? null,
            'title' => $data['title'] ?? null,
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
            'deleted_at' => $data['deleted_at'] ?? null,
            'archived_at' => $data['archived_at'] ?? null,
            'cabinet_id' => $data['cabinet_id'] ?? null,
            'employee_id' => $data['employee_id'] ?? null,
            'department_id' => $data['department_id'] ?? null,
            'status_id' => $data['status_id'] ?? null,
            'phone' => $data['phone'] ?? null,
            'fax' => $data['fax'] ?? null,
            'email' => $data['email'] ?? null,
            'description' => $data['description'] ?? null,
            'code' => $data['code'] ?? null,
            'external_code' => $data['external_code'] ?? null,
            'discounts_and_prices' => $data['discounts_and_prices'] ?? null,
            'discount_card_number' => $data['discount_card_number'] ?? null,
        ], fn($value) => $value !== null);

        /** @var bool */
        $result['shared_access'] = $data['shared_access'] ?? false;
        /** @var bool */
        $result['is_buyer'] = $data['is_buyer'] ?? false;
        /** @var bool */
        $result['is_supplier'] = $data['is_supplier'] ?? false;
        /** @var bool */
        $result['is_default'] = $data['is_default'] ?? false;

        // Связи для index (только основная информация)
        if (isset($data['groups']) && !empty($data['groups'])) {
            /** @var array */
            $result['groups'] = $this->processGroups($data['groups']);
        }

        if (isset($data['status']) && !empty($data['status'])) {
            /** @var object */
            $result['status'] = $this->processStatus($data['status']);
        }

        if (isset($data['details']) && !empty($data['details'])) {
            /** @var object */
            $result['details'] = $this->processDetails($data['details']);
        }

        return $result;
    }

    private function processGroups($groups): array
    {
        if (is_string($groups)) {
            $groups = json_decode($groups, true);
        }

        if (!is_array($groups) || empty($groups)) {
            return [];
        }

        return array_map(function ($group) {
            return array_filter([
                'id' => $group['id'] ?? null,
                'name' => $group['name'] ?? null,
                'contractor_group_name' => $group['contractor_group_name'] ?? null,
                'group_id' => $group['group_id'] ?? null,
            ], fn($value) => $value !== null);
        }, $groups);
    }

    private function processStatus($status): array
    {
        if (is_string($status)) {
            $status = json_decode($status, true);
        }

        if (empty($status)) {
            return [];
        }

        return array_filter([
            'id' => $status['id'] ?? null,
            'name' => $status['name'] ?? null,
            'color' => $status['color'] ?? null,
            'type_id' => $status['type_id'] ?? null,
            'cabinet_id' => $status['cabinet_id'] ?? null,
            'created_at' => $status['created_at'] ?? null,
            'deleted_at' => $status['deleted_at'] ?? null,
            'updated_at' => $status['updated_at'] ?? null,
        ], fn($value) => $value !== null);
    }

    private function processDetails($details): array
    {
        if (is_string($details)) {
            $details = json_decode($details, true);
        }

        if (empty($details)) {
            return [];
        }

        return array_filter([
            'taxation_type' => $details['taxation_type'] ?? null,
            'type' => $details['type'] ?? null,
            'inn' => $details['inn'] ?? null,
            'kpp' => $details['kpp'] ?? null,
            'ogrn' => $details['ogrn'] ?? null,
            'okpo' => $details['okpo'] ?? null,
            'full_name' => $details['full_name'] ?? null,
            'firstname' => $details['firstname'] ?? null,
            'patronymic' => $details['patronymic'] ?? null,
            'lastname' => $details['lastname'] ?? null,
            'ogrnip' => $details['ogrnip'] ?? null,
            'certificate_number' => $details['certificate_number'] ?? null,
            'certificate_date' => $details['certificate_date'] ?? null,
        ], fn($value) => $value !== null);
    }
}
