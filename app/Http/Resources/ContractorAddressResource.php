<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorAddressResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return array_filter([
            'postcode' => $this->postcode,
            'country' => $this->country,
            'region' => $this->region,
            'city' => $this->city,
            'street' => $this->street,
            'house' => $this->house,
            'office' => $this->office,
            'other' => $this->other,
            'comment' => $this->comment,
        ], fn($value) => $value !== null);
    }
}
