<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorAccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            /** @var bool */
            'is_main' => $this->is_main,
            'bik' => $this->bik,
            'correspondent_account' => $this->correspondent_account,
            'payment_account' => $this->payment_account,
            'bank' => $this->bank,
            'address' => $this->address,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
