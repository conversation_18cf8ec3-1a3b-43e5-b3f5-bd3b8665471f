<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Преобразуем ресурс в массив если это объект
        $data = is_array($this->resource) ? $this->resource : (array) $this->resource;

        return [
            'id' => $data['id'] ?? null,
            'title' => $data['title'] ?? null,
            'created_at' => $data['created_at'] ?? null,
            'updated_at' => $data['updated_at'] ?? null,
            'deleted_at' => $data['deleted_at'] ?? null,
            'archived_at' => $data['archived_at'] ?? null,
            'cabinet_id' => $data['cabinet_id'] ?? null,
            /** @var bool */
            'shared_access' => $data['shared_access'] ?? false,
            'employee_id' => $data['employee_id'] ?? null,
            'department_id' => $data['department_id'] ?? null,
            'status_id' => $data['status_id'] ?? null,
            /** @var bool */
            'is_buyer' => $data['is_buyer'] ?? false,
            /** @var bool */
            'is_supplier' => $data['is_supplier'] ?? false,
            'phone' => $data['phone'] ?? null,
            'fax' => $data['fax'] ?? null,
            'email' => $data['email'] ?? null,
            'description' => $data['description'] ?? null,
            'code' => $data['code'] ?? null,
            'external_code' => $data['external_code'] ?? null,
            'discounts_and_prices' => $data['discounts_and_prices'] ?? null,
            'discount_card_number' => $data['discount_card_number'] ?? null,
            /** @var bool */
            'is_default' => $data['is_default'] ?? false,

            /** @var ContractorGroupResource[] */
            'groups' => $this->getResourceCollection('groups', ContractorGroupResource::class, $data),
            /** @var StatusResource */
            'status' => $this->getResourceObject('status', StatusResource::class, $data),
            /** @var ContractorDetailResource */
            'details' => $this->getResourceObject('details', ContractorDetailResource::class, $data),

            /** @var ContractorAccountResource[] */
            'accounts' => $this->getResourceCollection('accounts', ContractorAccountResource::class, $data),
            /** @var ContractorContactResource[] */
            'contacts' => $this->getResourceCollection('contacts', ContractorContactResource::class, $data),
            /** @var ContractorGroupResource[] */
            'contractor_groups' => $this->getResourceCollection('contractor_groups', ContractorGroupResource::class, $data),
            /** @var ContractorDetailResource */
            'detail' => $this->getResourceObject('detail', ContractorDetailResource::class, $data),
            /** @var ContractorAddressResource */
            'address' => $this->getResourceObject('address', ContractorAddressResource::class, $data),
            /** @var FileResource[] */
            'files' => $this->getResourceCollection('files', FileResource::class, $data),
        ];
    }

    private function getResourceCollection(string $field, string $resourceClass, array $data): array
    {
        if (!isset($data[$field])) {
            return [];
        }

        $fieldData = is_string($data[$field]) ? json_decode($data[$field], true) : $data[$field];

        if (!is_array($fieldData) || empty($fieldData)) {
            return [];
        }

        return $resourceClass::collection(collect($fieldData))->toArray(request());
    }

    private function getResourceObject(string $field, string $resourceClass, array $data): array
    {
        if (!isset($data[$field])) {
            return [];
        }

        $fieldData = is_string($data[$field]) ? json_decode($data[$field], true) : $data[$field];

        if (empty($fieldData)) {
            return [];
        }

        return $resourceClass::make((object)$fieldData)->toArray(request());
    }
}
